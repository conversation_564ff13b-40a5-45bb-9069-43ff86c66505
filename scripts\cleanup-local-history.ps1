# PhotonRender - Local History Cleanup Script
# Pulisce automaticamente la Local History di VS Code per ottimizzare spazio disco
# Uso: .\scripts\cleanup-local-history.ps1 [-DaysOld 30] [-DryRun]

param(
    [int]$DaysOld = 30,
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Colori per output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Cyan = "Cyan"

Write-Host "🧹 PhotonRender - Local History Cleanup" -ForegroundColor $Cyan
Write-Host "=======================================" -ForegroundColor $Cyan
Write-Host ""

# Percorsi Local History per diversi sistemi
$historyPaths = @()

# Windows
if ($env:APPDATA) {
    $historyPaths += "$env:APPDATA\Code\User\History"
    $historyPaths += "$env:APPDATA\Code - Insiders\User\History"
}

# Verifica percorsi esistenti
$validPaths = @()
foreach ($path in $historyPaths) {
    if (Test-Path $path) {
        $validPaths += $path
        Write-Host "✅ Trovato: $path" -ForegroundColor $Green
    } else {
        Write-Host "❌ Non trovato: $path" -ForegroundColor $Yellow
    }
}

if ($validPaths.Count -eq 0) {
    Write-Host "❌ Nessuna cartella Local History trovata!" -ForegroundColor $Red
    exit 1
}

Write-Host ""

# Funzione per formattare dimensioni
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size bytes"
    }
}

# Analisi e pulizia per ogni percorso
$totalSizeBefore = 0
$totalSizeAfter = 0
$totalFilesRemoved = 0

foreach ($historyPath in $validPaths) {
    Write-Host "📁 Analizzando: $historyPath" -ForegroundColor $Cyan
    
    # Calcola dimensione totale prima
    $allFiles = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue
    $sizeBefore = ($allFiles | Measure-Object -Property Length -Sum).Sum
    $totalSizeBefore += $sizeBefore
    
    Write-Host "   📊 Dimensione attuale: $(Format-FileSize $sizeBefore)" -ForegroundColor $Yellow
    Write-Host "   📄 File totali: $($allFiles.Count)" -ForegroundColor $Yellow
    
    # Trova file vecchi
    $cutoffDate = (Get-Date).AddDays(-$DaysOld)
    $oldFiles = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue | 
                Where-Object { $_.LastWriteTime -lt $cutoffDate }
    
    if ($oldFiles.Count -eq 0) {
        Write-Host "   ✅ Nessun file da rimuovere (più vecchio di $DaysOld giorni)" -ForegroundColor $Green
        continue
    }
    
    $oldFilesSize = ($oldFiles | Measure-Object -Property Length -Sum).Sum
    Write-Host "   🗑️  File da rimuovere: $($oldFiles.Count)" -ForegroundColor $Red
    Write-Host "   💾 Spazio da liberare: $(Format-FileSize $oldFilesSize)" -ForegroundColor $Red
    
    if ($Verbose) {
        Write-Host "   📋 File più vecchi:" -ForegroundColor $Yellow
        $oldFiles | Sort-Object LastWriteTime | Select-Object -First 10 | ForEach-Object {
            Write-Host "      - $($_.Name) ($(Format-FileSize $_.Length)) - $($_.LastWriteTime)" -ForegroundColor $Yellow
        }
        if ($oldFiles.Count -gt 10) {
            Write-Host "      ... e altri $($oldFiles.Count - 10) file" -ForegroundColor $Yellow
        }
    }
    
    if ($DryRun) {
        Write-Host "   🔍 DRY RUN: File NON rimossi (usa senza -DryRun per rimuovere)" -ForegroundColor $Yellow
    } else {
        # Rimuovi file vecchi
        try {
            $oldFiles | Remove-Item -Force -ErrorAction Stop
            Write-Host "   ✅ $($oldFiles.Count) file rimossi con successo" -ForegroundColor $Green
            $totalFilesRemoved += $oldFiles.Count
        } catch {
            Write-Host "   ❌ Errore durante rimozione: $($_.Exception.Message)" -ForegroundColor $Red
        }
        
        # Rimuovi cartelle vuote
        try {
            Get-ChildItem $historyPath -Recurse -Directory -ErrorAction SilentlyContinue | 
            Where-Object { (Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0 } |
            Remove-Item -Force -ErrorAction SilentlyContinue
        } catch {
            # Ignora errori per cartelle vuote
        }
    }
    
    # Calcola dimensione dopo pulizia
    if (-not $DryRun) {
        $allFilesAfter = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue
        $sizeAfter = ($allFilesAfter | Measure-Object -Property Length -Sum).Sum
        $totalSizeAfter += $sizeAfter
        
        $spaceSaved = $sizeBefore - $sizeAfter
        Write-Host "   💾 Spazio liberato: $(Format-FileSize $spaceSaved)" -ForegroundColor $Green
        Write-Host "   📊 Dimensione finale: $(Format-FileSize $sizeAfter)" -ForegroundColor $Green
    }
    
    Write-Host ""
}

# Riepilogo finale
Write-Host "📊 RIEPILOGO FINALE" -ForegroundColor $Cyan
Write-Host "===================" -ForegroundColor $Cyan

if ($DryRun) {
    Write-Host "🔍 MODALITÀ DRY RUN - Nessun file rimosso" -ForegroundColor $Yellow
    Write-Host "💾 Spazio potenzialmente liberabile: $(Format-FileSize ($totalSizeBefore - $totalSizeAfter))" -ForegroundColor $Yellow
} else {
    $totalSpaceSaved = $totalSizeBefore - $totalSizeAfter
    Write-Host "✅ File rimossi: $totalFilesRemoved" -ForegroundColor $Green
    Write-Host "💾 Spazio liberato: $(Format-FileSize $totalSpaceSaved)" -ForegroundColor $Green
    Write-Host "📊 Dimensione prima: $(Format-FileSize $totalSizeBefore)" -ForegroundColor $Yellow
    Write-Host "📊 Dimensione dopo: $(Format-FileSize $totalSizeAfter)" -ForegroundColor $Green
    
    if ($totalSpaceSaved -gt 0) {
        $percentageSaved = [math]::Round(($totalSpaceSaved / $totalSizeBefore) * 100, 1)
        Write-Host "📈 Riduzione: $percentageSaved%" -ForegroundColor $Green
    }
}

Write-Host ""
Write-Host "🎯 RACCOMANDAZIONI:" -ForegroundColor $Cyan
Write-Host "- Esegui questo script mensilmente per mantenere ottimizzata la Local History" -ForegroundColor $Yellow
Write-Host "- Usa -DryRun per vedere cosa verrà rimosso prima di eseguire la pulizia" -ForegroundColor $Yellow
Write-Host "- Usa -Verbose per vedere dettagli sui file da rimuovere" -ForegroundColor $Yellow
Write-Host "- Le configurazioni VS Code in .vscode/settings.json limitano automaticamente nuovi salvataggi" -ForegroundColor $Yellow

Write-Host ""
Write-Host "✅ Pulizia Local History completata!" -ForegroundColor $Green
