# PhotonRender - Simple Local History Cleanup
# Pulisce automaticamente la Local History di VS Code

param(
    [int]$DaysOld = 30,
    [switch]$DryRun = $false
)

Write-Host "PhotonRender - Local History Cleanup" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Funzione per formattare dimensioni
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size bytes"
    }
}

# Percorso Local History
$historyPath = "$env:APPDATA\Code\User\History"

Write-Host "Analizzando: $historyPath" -ForegroundColor Cyan

if (-not (Test-Path $historyPath)) {
    Write-Host "Cartella Local History non trovata!" -ForegroundColor Red
    exit 1
}

# Calcola dimensione totale prima
$allFiles = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue
$sizeBefore = ($allFiles | Measure-Object -Property Length -Sum).Sum

Write-Host "Dimensione attuale: $(Format-FileSize $sizeBefore)" -ForegroundColor Yellow
Write-Host "File totali: $($allFiles.Count)" -ForegroundColor Yellow

# Trova file vecchi
$cutoffDate = (Get-Date).AddDays(-$DaysOld)
$oldFiles = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue | 
            Where-Object { $_.LastWriteTime -lt $cutoffDate }

if ($oldFiles.Count -eq 0) {
    Write-Host "Nessun file da rimuovere (piu vecchio di $DaysOld giorni)" -ForegroundColor Green
    exit 0
}

$oldFilesSize = ($oldFiles | Measure-Object -Property Length -Sum).Sum
Write-Host "File da rimuovere: $($oldFiles.Count)" -ForegroundColor Red
Write-Host "Spazio da liberare: $(Format-FileSize $oldFilesSize)" -ForegroundColor Red

if ($DryRun) {
    Write-Host "DRY RUN: File NON rimossi (usa senza -DryRun per rimuovere)" -ForegroundColor Yellow
} else {
    # Rimuovi file vecchi
    try {
        $oldFiles | Remove-Item -Force -ErrorAction Stop
        Write-Host "$($oldFiles.Count) file rimossi con successo" -ForegroundColor Green
        
        # Rimuovi cartelle vuote
        Get-ChildItem $historyPath -Recurse -Directory -ErrorAction SilentlyContinue | 
        Where-Object { (Get-ChildItem $_.FullName -ErrorAction SilentlyContinue).Count -eq 0 } |
        Remove-Item -Force -ErrorAction SilentlyContinue
        
        # Calcola dimensione dopo pulizia
        $allFilesAfter = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue
        $sizeAfter = ($allFilesAfter | Measure-Object -Property Length -Sum).Sum
        
        $spaceSaved = $sizeBefore - $sizeAfter
        Write-Host "Spazio liberato: $(Format-FileSize $spaceSaved)" -ForegroundColor Green
        Write-Host "Dimensione finale: $(Format-FileSize $sizeAfter)" -ForegroundColor Green
        
    } catch {
        Write-Host "Errore durante rimozione: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RACCOMANDAZIONI:" -ForegroundColor Cyan
Write-Host "- Esegui questo script mensilmente per mantenere ottimizzata la Local History" -ForegroundColor Yellow
Write-Host "- Usa -DryRun per vedere cosa verra rimosso prima di eseguire la pulizia" -ForegroundColor Yellow
Write-Host "- Le configurazioni VS Code in .vscode/settings.json limitano automaticamente nuovi salvataggi" -ForegroundColor Yellow

Write-Host ""
Write-Host "Pulizia Local History completata!" -ForegroundColor Green
