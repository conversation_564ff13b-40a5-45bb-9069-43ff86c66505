# PhotonRender - Simple Local History Monitor
# Monitora l'uso della Local History di VS Code

Write-Host "PhotonRender - Local History Monitor" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Funzione per formattare dimensioni
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size bytes"
    }
}

# Percorsi Local History
$historyPath = "$env:APPDATA\Code\User\History"

Write-Host "Analizzando: $historyPath" -ForegroundColor Cyan

if (-not (Test-Path $historyPath)) {
    Write-Host "Cartella Local History non trovata!" -ForegroundColor Red
    exit 1
}

# Analizza file
$allFiles = Get-ChildItem $historyPath -Recurse -File -ErrorAction SilentlyContinue
$totalSize = ($allFiles | Measure-Object -Property Length -Sum).Sum

Write-Host "File totali: $($allFiles.Count)" -ForegroundColor Green
Write-Host "Dimensione totale: $(Format-FileSize $totalSize)" -ForegroundColor Green
Write-Host ""

# Analisi per età
$now = Get-Date
$last7Days = $allFiles | Where-Object { $_.LastWriteTime -gt $now.AddDays(-7) }
$last30Days = $allFiles | Where-Object { $_.LastWriteTime -gt $now.AddDays(-30) }
$older30Days = $allFiles | Where-Object { $_.LastWriteTime -le $now.AddDays(-30) }

Write-Host "Distribuzione per eta:" -ForegroundColor Magenta
Write-Host "  Ultimi 7 giorni: $($last7Days.Count) file" -ForegroundColor Yellow
Write-Host "  Ultimi 30 giorni: $($last30Days.Count) file" -ForegroundColor Yellow
Write-Host "  Piu di 30 giorni: $($older30Days.Count) file" -ForegroundColor Red

if ($older30Days.Count -gt 0) {
    $oldSize = ($older30Days | Measure-Object -Property Length -Sum).Sum
    Write-Host "  Spazio liberabile: $(Format-FileSize $oldSize)" -ForegroundColor Green
}

Write-Host ""

# Verifica configurazione
Write-Host "STATO CONFIGURAZIONE:" -ForegroundColor Cyan

$settingsPath = ".vscode\settings.json"
if (Test-Path $settingsPath) {
    Write-Host "  File .vscode/settings.json trovato" -ForegroundColor Green
    
    $content = Get-Content $settingsPath -Raw
    if ($content -match "workbench.localHistory.maxFileEntries") {
        Write-Host "  Local History limitata configurata" -ForegroundColor Green
    } else {
        Write-Host "  Local History non limitata" -ForegroundColor Yellow
    }
    
    if ($content -match "workbench.localHistory.exclude") {
        Write-Host "  Esclusioni configurate" -ForegroundColor Green
    } else {
        Write-Host "  Nessuna esclusione configurata" -ForegroundColor Yellow
    }
} else {
    Write-Host "  File .vscode/settings.json non trovato" -ForegroundColor Red
}

Write-Host ""
Write-Host "COMANDI UTILI:" -ForegroundColor Cyan
Write-Host "  Pulizia test: .\scripts\cleanup-local-history.ps1 -DryRun" -ForegroundColor Yellow
Write-Host "  Pulizia effettiva: .\scripts\cleanup-local-history.ps1" -ForegroundColor Yellow

Write-Host ""
Write-Host "Monitoraggio completato!" -ForegroundColor Green
