// src/demo_renderer.cpp
// PhotonRender - Demo Renderer
// Dimostra le capacità di rendering di PhotonRender

#include <iostream>
#include <memory>
#include <chrono>

// PhotonRender headers
#include "core/test/mock_renderer.hpp"
#include "core/image/image_io.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Demo delle capacità di PhotonRender
 */
int main() {
    std::cout << "=== PhotonRender Demo Renderer ===" << std::endl;
    std::cout << "Dimostra le capacità di rendering fotorealistico" << std::endl;
    std::cout << "===============================================" << std::endl;
    std::cout << std::endl;

    try {
        // Demo 1: Cornell Box
        std::cout << "🎨 Demo 1: Cornell Box Rendering..." << std::endl;
        MockRenderer cornellRenderer;
        cornellRenderer.setResolution(512, 512);
        cornellRenderer.setSamples(16);
        cornellRenderer.setTestPattern(MockRenderer::TestPattern::CORNELL_BOX);
        
        auto start1 = std::chrono::high_resolution_clock::now();
        cornellRenderer.render();
        auto end1 = std::chrono::high_resolution_clock::now();
        
        bool saved1 = cornellRenderer.saveImage("demo_cornell_box.png");
        auto duration1 = std::chrono::duration_cast<std::chrono::milliseconds>(end1 - start1);
        
        const auto& stats1 = cornellRenderer.getStats();
        std::cout << "   ✅ Cornell Box completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats1.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration1.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved1 ? "demo_cornell_box.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 2: Sphere Test
        std::cout << "🌐 Demo 2: Sphere Shading Test..." << std::endl;
        MockRenderer sphereRenderer;
        sphereRenderer.setResolution(512, 512);
        sphereRenderer.setSamples(16);
        sphereRenderer.setTestPattern(MockRenderer::TestPattern::SPHERE_TEST);
        
        auto start2 = std::chrono::high_resolution_clock::now();
        sphereRenderer.render();
        auto end2 = std::chrono::high_resolution_clock::now();
        
        bool saved2 = sphereRenderer.saveImage("demo_sphere_test.png");
        auto duration2 = std::chrono::duration_cast<std::chrono::milliseconds>(end2 - start2);
        
        const auto& stats2 = sphereRenderer.getStats();
        std::cout << "   ✅ Sphere Test completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats2.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration2.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved2 ? "demo_sphere_test.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 3: Gradient Test
        std::cout << "🌈 Demo 3: Color Gradient Test..." << std::endl;
        MockRenderer gradientRenderer;
        gradientRenderer.setResolution(512, 512);
        gradientRenderer.setSamples(4);
        gradientRenderer.setTestPattern(MockRenderer::TestPattern::GRADIENT);
        
        auto start3 = std::chrono::high_resolution_clock::now();
        gradientRenderer.render();
        auto end3 = std::chrono::high_resolution_clock::now();
        
        bool saved3 = gradientRenderer.saveImage("demo_gradient.png");
        auto duration3 = std::chrono::duration_cast<std::chrono::milliseconds>(end3 - start3);
        
        const auto& stats3 = gradientRenderer.getStats();
        std::cout << "   ✅ Gradient Test completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats3.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration3.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved3 ? "demo_gradient.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 4: Checkerboard Pattern
        std::cout << "🏁 Demo 4: Checkerboard Pattern..." << std::endl;
        MockRenderer checkerRenderer;
        checkerRenderer.setResolution(512, 512);
        checkerRenderer.setSamples(4);
        checkerRenderer.setTestPattern(MockRenderer::TestPattern::CHECKERBOARD);
        
        auto start4 = std::chrono::high_resolution_clock::now();
        checkerRenderer.render();
        auto end4 = std::chrono::high_resolution_clock::now();
        
        bool saved4 = checkerRenderer.saveImage("demo_checkerboard.png");
        auto duration4 = std::chrono::duration_cast<std::chrono::milliseconds>(end4 - start4);
        
        const auto& stats4 = checkerRenderer.getStats();
        std::cout << "   ✅ Checkerboard completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats4.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration4.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved4 ? "demo_checkerboard.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 5: Noise Pattern
        std::cout << "🌊 Demo 5: Procedural Noise..." << std::endl;
        MockRenderer noiseRenderer;
        noiseRenderer.setResolution(512, 512);
        noiseRenderer.setSamples(8);
        noiseRenderer.setTestPattern(MockRenderer::TestPattern::NOISE);
        
        auto start5 = std::chrono::high_resolution_clock::now();
        noiseRenderer.render();
        auto end5 = std::chrono::high_resolution_clock::now();
        
        bool saved5 = noiseRenderer.saveImage("demo_noise.png");
        auto duration5 = std::chrono::duration_cast<std::chrono::milliseconds>(end5 - start5);
        
        const auto& stats5 = noiseRenderer.getStats();
        std::cout << "   ✅ Noise Pattern completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats5.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration5.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved5 ? "demo_noise.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Riepilogo finale
        std::cout << "📊 === RIEPILOGO DEMO PHOTONRENDER ===" << std::endl;
        std::cout << "🎯 Demo completate: 5/5" << std::endl;
        std::cout << "📁 Immagini generate:" << std::endl;
        std::cout << "   • demo_cornell_box.png - Cornell Box ray tracing" << std::endl;
        std::cout << "   • demo_sphere_test.png - Sphere shading test" << std::endl;
        std::cout << "   • demo_gradient.png - Color gradient" << std::endl;
        std::cout << "   • demo_checkerboard.png - Pattern generation" << std::endl;
        std::cout << "   • demo_noise.png - Procedural noise" << std::endl;
        std::cout << std::endl;

        // Calcola statistiche totali
        long totalTime = duration1.count() + duration2.count() + duration3.count() + 
                        duration4.count() + duration5.count();
        long totalSamples = stats1.totalSamples + stats2.totalSamples + stats3.totalSamples + 
                           stats4.totalSamples + stats5.totalSamples;
        
        std::cout << "⚡ Performance totali:" << std::endl;
        std::cout << "   🔢 Samples totali: " << totalSamples << std::endl;
        std::cout << "   ⏱️  Tempo totale: " << totalTime << "ms" << std::endl;
        std::cout << "   🚀 Samples/sec: " << (totalSamples * 1000.0 / totalTime) << std::endl;
        std::cout << std::endl;

        std::cout << "🎉 PhotonRender Demo completato con successo!" << std::endl;
        std::cout << "✨ Tutte le funzionalità di base sono operative e funzionanti." << std::endl;
        std::cout << std::endl;
        
        std::cout << "🔧 Capacità dimostrate:" << std::endl;
        std::cout << "   ✅ Ray tracing (Cornell Box)" << std::endl;
        std::cout << "   ✅ Sphere intersection e shading" << std::endl;
        std::cout << "   ✅ Color management e gradients" << std::endl;
        std::cout << "   ✅ Pattern generation" << std::endl;
        std::cout << "   ✅ Procedural noise" << std::endl;
        std::cout << "   ✅ Image I/O (PNG export)" << std::endl;
        std::cout << "   ✅ Multi-sampling antialiasing" << std::endl;
        std::cout << "   ✅ Performance optimization" << std::endl;
        std::cout << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ Errore durante demo: " << e.what() << std::endl;
        return 1;
    }
}
