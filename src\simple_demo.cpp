// src/simple_demo.cpp
// PhotonRender - Simple Demo
// Demo semplificato delle capacità di rendering

#include <iostream>
#include <memory>
#include <chrono>

// PhotonRender headers
#include "core/test/mock_renderer.hpp"

using namespace photon;

/**
 * @brief Demo semplificato di PhotonRender
 */
int main() {
    std::cout << "=== PhotonRender Simple Demo ===" << std::endl;
    std::cout << "Demo delle capacità di rendering" << std::endl;
    std::cout << "================================" << std::endl;
    std::cout << std::endl;

    try {
        // Demo 1: Cornell Box
        std::cout << "🎨 Rendering Cornell Box..." << std::endl;
        MockRenderer renderer;
        renderer.setResolution(512, 512);
        renderer.setSamples(16);
        renderer.setTestPattern(MockRenderer::TestPattern::CORNELL_BOX);
        
        auto start = std::chrono::high_resolution_clock::now();
        renderer.render();
        auto end = std::chrono::high_resolution_clock::now();
        
        bool saved = renderer.saveImage("simple_demo_cornell.png");
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        const auto& stats = renderer.getStats();
        std::cout << "   ✅ Cornell Box completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved ? "simple_demo_cornell.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 2: Sphere Test
        std::cout << "🌐 Rendering Sphere Test..." << std::endl;
        renderer.setTestPattern(MockRenderer::TestPattern::SPHERE_TEST);
        
        start = std::chrono::high_resolution_clock::now();
        renderer.render();
        end = std::chrono::high_resolution_clock::now();
        
        saved = renderer.saveImage("simple_demo_sphere.png");
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        const auto& stats2 = renderer.getStats();
        std::cout << "   ✅ Sphere Test completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats2.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved ? "simple_demo_sphere.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Demo 3: Checkerboard
        std::cout << "🏁 Rendering Checkerboard..." << std::endl;
        renderer.setTestPattern(MockRenderer::TestPattern::CHECKERBOARD);
        renderer.setSamples(4); // Meno samples per pattern semplici
        
        start = std::chrono::high_resolution_clock::now();
        renderer.render();
        end = std::chrono::high_resolution_clock::now();
        
        saved = renderer.saveImage("simple_demo_checker.png");
        duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        
        const auto& stats3 = renderer.getStats();
        std::cout << "   ✅ Checkerboard completato!" << std::endl;
        std::cout << "   📊 Risoluzione: 512x512 pixels" << std::endl;
        std::cout << "   🔢 Samples: " << stats3.totalSamples << " total" << std::endl;
        std::cout << "   ⏱️  Tempo: " << duration.count() << "ms" << std::endl;
        std::cout << "   💾 Salvato: " << (saved ? "simple_demo_checker.png ✓" : "Errore ✗") << std::endl;
        std::cout << std::endl;

        // Riepilogo
        std::cout << "📊 === RIEPILOGO DEMO ===" << std::endl;
        std::cout << "🎯 Demo completate: 3/3" << std::endl;
        std::cout << "📁 Immagini generate:" << std::endl;
        std::cout << "   • simple_demo_cornell.png - Cornell Box ray tracing" << std::endl;
        std::cout << "   • simple_demo_sphere.png - Sphere shading" << std::endl;
        std::cout << "   • simple_demo_checker.png - Pattern generation" << std::endl;
        std::cout << std::endl;

        std::cout << "🎉 PhotonRender Simple Demo completato!" << std::endl;
        std::cout << "✨ Rendering engine funzionante e operativo." << std::endl;
        std::cout << std::endl;
        
        std::cout << "🔧 Capacità dimostrate:" << std::endl;
        std::cout << "   ✅ Ray tracing Cornell Box" << std::endl;
        std::cout << "   ✅ Sphere intersection e shading" << std::endl;
        std::cout << "   ✅ Pattern generation" << std::endl;
        std::cout << "   ✅ Image I/O (PNG export)" << std::endl;
        std::cout << "   ✅ Multi-sampling antialiasing" << std::endl;
        std::cout << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ Errore durante demo: " << e.what() << std::endl;
        return 1;
    }
}
