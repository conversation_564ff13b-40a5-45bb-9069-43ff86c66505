// src/test_advanced_normal_mapping.cpp
// PhotonRender - Advanced Normal Mapping System Test
// Tests advanced normal mapping features: intensity control, blending, optimization

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include "core/material/material.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"
#include "core/scene/intersection.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for advanced normal mapping system
 */
class AdvancedNormalMappingTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }
    
    /**
     * @brief Create test intersection with tangent space
     */
    Intersection createTestIntersection() {
        Intersection isect;
        isect.hit = true;
        isect.p = Vec3(0.0f, 0.0f, 0.0f);
        isect.n = Vec3(0.0f, 0.0f, 1.0f); // Up normal
        isect.uv = Vec2(0.5f, 0.5f);
        isect.hasUV = true;
        
        // Set up tangent space
        isect.dpdu = Vec3(1.0f, 0.0f, 0.0f); // Tangent
        isect.dpdv = Vec3(0.0f, 1.0f, 0.0f); // Bitangent
        isect.hasTangents = true;
        
        return isect;
    }

public:
    /**
     * @brief Test 1: Normal intensity control
     */
    bool testNormalIntensityControl() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test intensity clamping
            material->setNormalIntensity(-0.5f); // Should clamp to 0
            bool test1 = (material->getNormalIntensity() == 0.0f);
            
            material->setNormalIntensity(1.5f); // Should clamp to 1
            bool test2 = (material->getNormalIntensity() == 1.0f);
            
            material->setNormalIntensity(0.5f); // Should remain 0.5
            bool test3 = (material->getNormalIntensity() == 0.5f);
            
            // Test zero intensity returns original normal
            material->setNormalIntensity(0.0f);
            Intersection isect = createTestIntersection();
            Vec3 originalNormal = isect.n;
            Vec3 result = material->applyAdvancedNormalMapping(isect.getUV(), originalNormal, 
                                                             isect.getTangent(), isect.getBitangent());
            bool test4 = (result - originalNormal).length() < 1e-6f;
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("2.1 Normal Intensity Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("2.1 Normal Intensity Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Normal blend factor control
     */
    bool testNormalBlendControl() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test blend factor clamping
            material->setNormalBlendFactor(-0.5f); // Should clamp to 0
            bool test1 = (material->getNormalBlendFactor() == 0.0f);
            
            material->setNormalBlendFactor(1.5f); // Should clamp to 1
            bool test2 = (material->getNormalBlendFactor() == 1.0f);
            
            material->setNormalBlendFactor(0.3f); // Should remain 0.3
            bool test3 = (material->getNormalBlendFactor() == 0.3f);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("2.2 Normal Blend Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("2.2 Normal Blend Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: Advanced normal mapping without texture
     */
    bool testAdvancedNormalMappingNoTexture() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            Intersection isect = createTestIntersection();
            
            Vec3 originalNormal = isect.n;
            Vec3 result = material->applyAdvancedNormalMapping(isect.getUV(), originalNormal, 
                                                             isect.getTangent(), isect.getBitangent());
            
            // Should return original normal when no texture
            bool passed = (result - originalNormal).length() < 1e-6f;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("2.3 Advanced Normal Mapping (No Texture)", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("2.3 Advanced Normal Mapping (No Texture)", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Surface mapping integration
     */
    bool testSurfaceMappingIntegration() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            Intersection isect = createTestIntersection();
            
            // Test with different intensity values
            material->setNormalIntensity(0.5f);
            material->setBumpIntensity(0.3f);
            
            Vec3 originalNormal = isect.n;
            Vec3 result = material->applySurfaceMapping(isect.getUV(), originalNormal, 
                                                       isect.getTangent(), isect.getBitangent());
            
            // Should return original normal when no textures
            bool passed = (result - originalNormal).length() < 1e-6f;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("2.4 Surface Mapping Integration", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("2.4 Surface Mapping Integration", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            material->setNormalIntensity(0.8f);
            material->setNormalBlendFactor(0.3f);
            
            Intersection isect = createTestIntersection();
            const int iterations = 10000;
            
            // Benchmark advanced normal mapping performance
            auto perfStart = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i) {
                Vec2 uv(i * 0.0001f, i * 0.0001f);
                material->applyAdvancedNormalMapping(uv, isect.n, isect.getTangent(), isect.getBitangent());
            }
            
            auto perfEnd = std::chrono::high_resolution_clock::now();
            double totalTime = std::chrono::duration<double, std::nano>(perfEnd - perfStart).count();
            double avgTime = totalTime / iterations;
            
            // Performance target: < 150ns per call (more complex than basic)
            bool passed = avgTime < 150.0;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Avg: " + std::to_string(avgTime) + "ns per call";
            addTestResult("2.5 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("2.5 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Advanced Normal Mapping Test Suite ===" << std::endl;
        std::cout << "Testing advanced normal mapping features..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testNormalIntensityControl();
        allPassed &= testNormalBlendControl();
        allPassed &= testAdvancedNormalMappingNoTexture();
        allPassed &= testSurfaceMappingIntegration();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns << "ns)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Advanced normal mapping system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    AdvancedNormalMappingTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
