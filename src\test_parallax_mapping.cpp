// src/test_parallax_mapping.cpp
// PhotonRender - Parallax & Relief Mapping System Test
// Tests parallax mapping, steep parallax mapping, and relief mapping

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include "core/material/material.hpp"
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/vec3.hpp"
#include "core/math/color3.hpp"
#include "core/scene/intersection.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for parallax mapping system
 */
class ParallaxMappingTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }

public:
    /**
     * @brief Test 1: Parallax scale control
     */
    bool testParallaxScaleControl() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test scale clamping
            material->setParallaxScale(-0.5f); // Should clamp to 0
            bool test1 = (material->getParallaxScale() == 0.0f);
            
            material->setParallaxScale(1.5f); // Should clamp to 1
            bool test2 = (material->getParallaxScale() == 1.0f);
            
            material->setParallaxScale(0.3f); // Should remain 0.3
            bool test3 = (material->getParallaxScale() == 0.3f);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.1 Parallax Scale Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1 Parallax Scale Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Parallax layers control
     */
    bool testParallaxLayersControl() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test layers clamping
            material->setParallaxLayers(5); // Should clamp to 8
            bool test1 = (material->getParallaxLayers() == 8);
            
            material->setParallaxLayers(100); // Should clamp to 64
            bool test2 = (material->getParallaxLayers() == 64);
            
            material->setParallaxLayers(32); // Should remain 32
            bool test3 = (material->getParallaxLayers() == 32);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2 Parallax Layers Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2 Parallax Layers Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: Relief mapping control
     */
    bool testReliefMappingControl() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            // Test relief mapping enable/disable
            material->setReliefMappingEnabled(true);
            bool test1 = material->isReliefMappingEnabled();
            
            material->setReliefMappingEnabled(false);
            bool test2 = !material->isReliefMappingEnabled();
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.3 Relief Mapping Control", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3 Relief Mapping Control", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Basic parallax mapping
     */
    bool testBasicParallaxMapping() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            Vec2 uv(0.5f, 0.5f);
            Vec3 viewDir(0.0f, 0.0f, 1.0f); // Looking straight down
            
            // Test without texture (should return original UV)
            Vec2 result = material->applyParallaxMapping(uv, viewDir);
            bool test1 = (result - uv).length() < 1e-6f;
            
            // Test with zero scale (should return original UV)
            material->setParallaxScale(0.0f);
            result = material->applyParallaxMapping(uv, viewDir);
            bool test2 = (result - uv).length() < 1e-6f;
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.4 Basic Parallax Mapping", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.4 Basic Parallax Mapping", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: Steep parallax mapping
     */
    bool testSteepParallaxMapping() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            Vec2 uv(0.5f, 0.5f);
            Vec3 viewDir(0.0f, 0.0f, 1.0f);
            
            // Test without texture (should return original UV)
            Vec2 result = material->applySteepParallaxMapping(uv, viewDir);
            bool test1 = (result - uv).length() < 1e-6f;
            
            // Test with zero scale (should return original UV)
            material->setParallaxScale(0.0f);
            result = material->applySteepParallaxMapping(uv, viewDir);
            bool test2 = (result - uv).length() < 1e-6f;
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.5 Steep Parallax Mapping", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.5 Steep Parallax Mapping", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 6: Relief mapping
     */
    bool testReliefMapping() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            
            Vec2 uv(0.5f, 0.5f);
            Vec3 viewDir(0.0f, 0.0f, 1.0f);
            
            // Test without texture (should return original UV)
            Vec2 result = material->applyReliefMapping(uv, viewDir);
            bool test1 = (result - uv).length() < 1e-6f;
            
            // Test with relief mapping disabled (should return original UV)
            material->setReliefMappingEnabled(false);
            result = material->applyReliefMapping(uv, viewDir);
            bool test2 = (result - uv).length() < 1e-6f;
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.6 Relief Mapping", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.6 Relief Mapping", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 7: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto material = std::make_shared<PBRMaterial>();
            material->setParallaxScale(0.1f);
            material->setParallaxLayers(16);
            
            Vec2 uv(0.5f, 0.5f);
            Vec3 viewDir(0.0f, 0.0f, 1.0f);
            const int iterations = 1000; // Fewer iterations due to complexity
            
            // Benchmark parallax mapping performance
            auto perfStart = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i) {
                Vec2 testUV(0.5f + i * 0.0001f, 0.5f + i * 0.0001f);
                material->applyParallaxMapping(testUV, viewDir);
                material->applySteepParallaxMapping(testUV, viewDir);
                material->applyReliefMapping(testUV, viewDir);
            }
            
            auto perfEnd = std::chrono::high_resolution_clock::now();
            double totalTime = std::chrono::duration<double, std::nano>(perfEnd - perfStart).count();
            double avgTime = totalTime / (iterations * 3); // 3 methods tested
            
            // Performance target: < 1000ns per call (more complex algorithms)
            bool passed = avgTime < 1000.0;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Avg: " + std::to_string(avgTime) + "ns per call";
            addTestResult("3.7 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.7 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Parallax & Relief Mapping Test Suite ===" << std::endl;
        std::cout << "Testing parallax mapping advanced features..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testParallaxScaleControl();
        allPassed &= testParallaxLayersControl();
        allPassed &= testReliefMappingControl();
        allPassed &= testBasicParallaxMapping();
        allPassed &= testSteepParallaxMapping();
        allPassed &= testReliefMapping();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns << "ns)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Parallax & relief mapping system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    ParallaxMappingTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
