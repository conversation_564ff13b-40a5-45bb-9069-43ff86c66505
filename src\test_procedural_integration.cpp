// src/test_procedural_integration.cpp - Integration test suite for procedural textures
// PhotonRender Procedural Texture Integration Test Suite

#include <iostream>
#include <iomanip>
#include <chrono>
#include <memory>
#include <vector>
#include "core/texture/texture.hpp"

using namespace photon;

/**
 * @brief Integration test suite for all procedural texture systems
 */
class ProceduralIntegrationTest {
public:
    /**
     * @brief Run all integration tests
     */
    static bool runAllTests() {
        std::cout << "=== PhotonRender Procedural Texture Integration Test Suite ===\n\n";
        
        int passed = 0;
        int total = 0;
        
        // Integration tests
        if (testNoiseTextureSystem()) passed++; total++;
        if (testPatternTextureSystem()) passed++; total++;
        if (testGradientTextureSystem()) passed++; total++;
        if (testCombinedTextureUsage()) passed++; total++;
        if (testPerformanceComparison()) passed++; total++;
        if (testMemoryUsage()) passed++; total++;
        if (testQualityAssurance()) passed++; total++;
        if (testEdgeCasesIntegration()) passed++; total++;
        
        std::cout << "\n=== Integration Test Results ===\n";
        std::cout << "Passed: " << passed << "/" << total << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL INTEGRATION TESTS PASSED! Procedural Texture System fully functional!\n";
            return true;
        } else {
            std::cout << "❌ Some integration tests failed. Check system integration.\n";
            return false;
        }
    }

private:
    /**
     * @brief Test complete noise texture system
     */
    static bool testNoiseTextureSystem() {
        std::cout << "Testing noise texture system integration... ";
        
        try {
            // Test all noise types
            std::vector<NoiseType> noiseTypes = {
                NoiseType::PERLIN,
                NoiseType::SIMPLEX,
                NoiseType::WORLEY
            };
            
            for (auto noiseType : noiseTypes) {
                NoiseTexture noise(noiseType, 4.0f, 1.0f, FractalParams::defaultParams());
                
                // Test sampling
                Color3 color = noise.sample(Vec2(0.5f, 0.5f));
                float value = noise.sampleFloat(Vec2(0.5f, 0.5f));
                
                if (std::isnan(color.r) || std::isnan(value)) {
                    std::cout << "[FAIL] - Noise type " << static_cast<int>(noiseType) << " produces NaN\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test complete pattern texture system
     */
    static bool testPatternTextureSystem() {
        std::cout << "Testing pattern texture system integration... ";
        
        try {
            // Test all pattern types
            std::vector<PatternType> patternTypes = {
                PatternType::CHECKERBOARD,
                PatternType::STRIPES,
                PatternType::DOTS,
                PatternType::GRID
            };
            
            for (auto patternType : patternTypes) {
                PatternTexture pattern(patternType, 2.0f, Color3(1.0f), Color3(0.0f));
                
                // Test sampling
                Color3 color = pattern.sample(Vec2(0.5f, 0.5f));
                float value = pattern.sampleFloat(Vec2(0.5f, 0.5f));
                
                if (std::isnan(color.r) || std::isnan(value)) {
                    std::cout << "[FAIL] - Pattern type " << static_cast<int>(patternType) << " produces NaN\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test complete gradient texture system
     */
    static bool testGradientTextureSystem() {
        std::cout << "Testing gradient texture system integration... ";
        
        try {
            // Test all gradient types
            std::vector<GradientType> gradientTypes = {
                GradientType::LINEAR,
                GradientType::RADIAL,
                GradientType::ANGULAR
            };
            
            for (auto gradientType : gradientTypes) {
                GradientTexture gradient(gradientType, Color3(1.0f, 0.0f, 0.0f), Color3(0.0f, 0.0f, 1.0f));
                
                // Test sampling
                Color3 color = gradient.sample(Vec2(0.5f, 0.5f));
                float value = gradient.sampleFloat(Vec2(0.5f, 0.5f));
                
                if (std::isnan(color.r) || std::isnan(value)) {
                    std::cout << "[FAIL] - Gradient type " << static_cast<int>(gradientType) << " produces NaN\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test combined texture usage scenarios
     */
    static bool testCombinedTextureUsage() {
        std::cout << "Testing combined texture usage... ";
        
        try {
            // Create different texture types
            auto noise = std::make_unique<NoiseTexture>(4.0f, 1.0f, 4);
            auto pattern = std::make_unique<PatternTexture>(PatternType::CHECKERBOARD, 8.0f);
            auto gradient = std::make_unique<GradientTexture>(GradientType::RADIAL, Color3(1.0f), Color3(0.0f));
            
            // Test sampling all at same coordinates
            Vec2 testUV(0.3f, 0.7f);
            
            Color3 noiseColor = noise->sample(testUV);
            Color3 patternColor = pattern->sample(testUV);
            Color3 gradientColor = gradient->sample(testUV);
            
            // Test that all produce valid results
            if (std::isnan(noiseColor.r) || std::isnan(patternColor.r) || std::isnan(gradientColor.r)) {
                std::cout << "[FAIL] - Combined usage produces NaN\n";
                return false;
            }
            
            // Test that results are different (showing variety)
            if (std::abs(noiseColor.r - patternColor.r) < 0.01f && 
                std::abs(patternColor.r - gradientColor.r) < 0.01f) {
                std::cout << "[FAIL] - All textures produce same result\n";
                return false;
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test performance comparison across all texture types
     */
    static bool testPerformanceComparison() {
        std::cout << "Testing performance comparison... ";
        
        try {
            const int numSamples = 10000;
            
            // Test noise performance
            NoiseTexture noise(4.0f, 1.0f, 4);
            auto startTime = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                noise.sampleFloat(Vec2(u, v));
            }
            auto endTime = std::chrono::high_resolution_clock::now();
            auto noiseDuration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            float noiseTimeNs = float(noiseDuration.count()) / numSamples;
            
            // Test pattern performance
            PatternTexture pattern(PatternType::CHECKERBOARD, 8.0f);
            startTime = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                pattern.sampleFloat(Vec2(u, v));
            }
            endTime = std::chrono::high_resolution_clock::now();
            auto patternDuration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            float patternTimeNs = float(patternDuration.count()) / numSamples;
            
            // Test gradient performance
            GradientTexture gradient(GradientType::LINEAR, Color3(0.0f), Color3(1.0f));
            startTime = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < numSamples; ++i) {
                float u = float(i % 100) / 100.0f;
                float v = float(i / 100) / 100.0f;
                gradient.sampleFloat(Vec2(u, v));
            }
            endTime = std::chrono::high_resolution_clock::now();
            auto gradientDuration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - startTime);
            float gradientTimeNs = float(gradientDuration.count()) / numSamples;
            
            // All should be under 1000ns target
            if (noiseTimeNs > 1000.0f || patternTimeNs > 1000.0f || gradientTimeNs > 1000.0f) {
                std::cout << "[FAIL] - Performance targets not met\n";
                std::cout << "  Noise: " << noiseTimeNs << "ns, Pattern: " << patternTimeNs << "ns, Gradient: " << gradientTimeNs << "ns\n";
                return false;
            }
            
            std::cout << "[PASS] (Noise: " << std::fixed << std::setprecision(1) << noiseTimeNs 
                      << "ns, Pattern: " << patternTimeNs << "ns, Gradient: " << gradientTimeNs << "ns)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test memory usage patterns
     */
    static bool testMemoryUsage() {
        std::cout << "Testing memory usage... ";
        
        try {
            // Create multiple texture instances
            std::vector<std::unique_ptr<Texture>> textures;
            
            for (int i = 0; i < 100; ++i) {
                textures.push_back(std::make_unique<NoiseTexture>(4.0f, 1.0f, 4));
                textures.push_back(std::make_unique<PatternTexture>(PatternType::CHECKERBOARD, 8.0f));
                textures.push_back(std::make_unique<GradientTexture>(GradientType::LINEAR, Color3(0.0f), Color3(1.0f)));
            }
            
            // Test that all are functional
            for (auto& texture : textures) {
                Color3 color = texture->sample(Vec2(0.5f, 0.5f));
                if (std::isnan(color.r)) {
                    std::cout << "[FAIL] - Memory usage test produces NaN\n";
                    return false;
                }
            }
            
            std::cout << "[PASS] (Created " << textures.size() << " texture instances)\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test quality assurance across all systems
     */
    static bool testQualityAssurance() {
        std::cout << "Testing quality assurance... ";
        
        try {
            // Test value ranges
            NoiseTexture noise(4.0f, 1.0f, 4);
            PatternTexture pattern(PatternType::CHECKERBOARD, 8.0f);
            GradientTexture gradient(GradientType::LINEAR, Color3(0.0f), Color3(1.0f));
            
            // Sample multiple points and check ranges
            for (int i = 0; i < 100; ++i) {
                float u = float(i % 10) / 10.0f;
                float v = float(i / 10) / 10.0f;
                Vec2 uv(u, v);
                
                Color3 noiseColor = noise.sample(uv);
                Color3 patternColor = pattern.sample(uv);
                Color3 gradientColor = gradient.sample(uv);
                
                // Check that all values are in valid range [0,1]
                if (noiseColor.r < 0.0f || noiseColor.r > 1.0f ||
                    patternColor.r < 0.0f || patternColor.r > 1.0f ||
                    gradientColor.r < 0.0f || gradientColor.r > 1.0f) {
                    std::cout << "[FAIL] - Values outside [0,1] range\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
    
    /**
     * @brief Test edge cases across all systems
     */
    static bool testEdgeCasesIntegration() {
        std::cout << "Testing edge cases integration... ";
        
        try {
            // Test extreme UV coordinates
            std::vector<Vec2> extremeUVs = {
                Vec2(-10.0f, -10.0f),
                Vec2(10.0f, 10.0f),
                Vec2(0.0f, 0.0f),
                Vec2(1.0f, 1.0f),
                Vec2(0.5f, 0.5f)
            };
            
            NoiseTexture noise(4.0f, 1.0f, 4);
            PatternTexture pattern(PatternType::CHECKERBOARD, 8.0f);
            GradientTexture gradient(GradientType::LINEAR, Color3(0.0f), Color3(1.0f));
            
            for (const auto& uv : extremeUVs) {
                Color3 noiseColor = noise.sample(uv);
                Color3 patternColor = pattern.sample(uv);
                Color3 gradientColor = gradient.sample(uv);
                
                // Check for NaN or infinite values
                if (std::isnan(noiseColor.r) || std::isinf(noiseColor.r) ||
                    std::isnan(patternColor.r) || std::isinf(patternColor.r) ||
                    std::isnan(gradientColor.r) || std::isinf(gradientColor.r)) {
                    std::cout << "[FAIL] - Extreme UV produces NaN/Inf\n";
                    return false;
                }
            }
            
            std::cout << "[PASS]\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[FAIL] - Exception: " << e.what() << "\n";
            return false;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    bool success = ProceduralIntegrationTest::runAllTests();
    return success ? 0 : 1;
}
