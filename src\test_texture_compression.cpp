// src/test_texture_compression.cpp
// PhotonRender - Texture Compression System Test
// Tests texture compression functionality, formats, and performance

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include "core/texture/texture.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for texture compression system
 */
class TextureCompressionTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }
    
    /**
     * @brief Create test texture with known pattern
     */
    std::shared_ptr<ImageTexture> createTestTexture(int width, int height, bool withAlpha = false) {
        int channels = withAlpha ? 4 : 3;
        std::vector<float> data(width * height * channels);
        
        // Create a simple gradient pattern
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = (y * width + x) * channels;
                
                // RGB gradient
                data[index] = static_cast<float>(x) / width;         // Red
                data[index + 1] = static_cast<float>(y) / height;    // Green
                data[index + 2] = 0.5f;                              // Blue
                
                if (withAlpha) {
                    data[index + 3] = 0.8f; // Alpha
                }
            }
        }
        
        return std::make_shared<ImageTexture>(width, height, channels, data.data());
    }

public:
    /**
     * @brief Test 1: Basic compression functionality
     */
    bool testBasicCompression() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            // Create test texture (must be multiple of 4 for DXT)
            auto texture = createTestTexture(64, 64, false);
            
            // Test compression
            CompressionSettings settings = CompressionSettings::defaultSettings();
            bool compressed = texture->compress(settings);
            
            // Verify compression
            bool test1 = compressed;
            bool test2 = texture->isCompressed();
            bool test3 = texture->getCompressionRatio() > 1.0f;
            bool test4 = texture->getMemoryUsage() > 0;
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Ratio: " + std::to_string(texture->getCompressionRatio()) + 
                                 "x, Memory: " + std::to_string(texture->getMemoryUsage()) + " bytes";
            addTestResult("3.1.1 Basic Compression", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.1 Basic Compression", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: DXT1 compression
     */
    bool testDXT1Compression() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(64, 64, false);
            
            CompressionSettings settings;
            settings.autoFormat = false;
            
            // Force DXT1 format by setting RGB format
            bool compressed = texture->compress(settings);
            
            // Test sampling after compression
            Color3 sample1 = texture->sample(Vec2(0.5f, 0.5f));
            Color3 sample2 = texture->sample(Vec2(0.0f, 0.0f));
            Color3 sample3 = texture->sample(Vec2(1.0f, 1.0f));
            
            // Verify samples are valid
            bool test1 = compressed;
            bool test2 = std::isfinite(sample1.r) && std::isfinite(sample1.g) && std::isfinite(sample1.b);
            bool test3 = std::isfinite(sample2.r) && std::isfinite(sample2.g) && std::isfinite(sample2.b);
            bool test4 = std::isfinite(sample3.r) && std::isfinite(sample3.g) && std::isfinite(sample3.b);
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.1.2 DXT1 Compression", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.2 DXT1 Compression", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: DXT5 compression with alpha
     */
    bool testDXT5Compression() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(64, 64, true); // With alpha
            
            CompressionSettings settings;
            settings.autoFormat = true; // Should choose DXT5 for alpha
            
            bool compressed = texture->compress(settings);
            
            // Test sampling
            Color3 sample = texture->sample(Vec2(0.5f, 0.5f));
            
            bool test1 = compressed;
            bool test2 = texture->isCompressed();
            bool test3 = std::isfinite(sample.r) && std::isfinite(sample.g) && std::isfinite(sample.b);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.1.3 DXT5 Compression (Alpha)", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.3 DXT5 Compression (Alpha)", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Compression quality levels
     */
    bool testCompressionQuality() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture1 = createTestTexture(64, 64, false);
            auto texture2 = createTestTexture(64, 64, false);
            auto texture3 = createTestTexture(64, 64, false);
            
            // Test different quality levels
            CompressionSettings lowQuality;
            lowQuality.quality = CompressionQuality::LOW;
            
            CompressionSettings mediumQuality;
            mediumQuality.quality = CompressionQuality::MEDIUM;
            
            CompressionSettings highQuality;
            highQuality.quality = CompressionQuality::HIGH;
            
            bool test1 = texture1->compress(lowQuality);
            bool test2 = texture2->compress(mediumQuality);
            bool test3 = texture3->compress(highQuality);
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.1.4 Compression Quality Levels", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.4 Compression Quality Levels", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: Memory usage validation
     */
    bool testMemoryUsage() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(128, 128, false);
            
            // Get original memory usage
            size_t originalMemory = texture->getMemoryUsage();
            
            // Compress texture
            CompressionSettings settings = CompressionSettings::defaultSettings();
            bool compressed = texture->compress(settings);
            
            // Get compressed memory usage
            size_t compressedMemory = texture->getMemoryUsage();
            float ratio = texture->getCompressionRatio();
            
            // Verify memory reduction
            bool test1 = compressed;
            bool test2 = compressedMemory < originalMemory;
            bool test3 = ratio > 1.0f;
            bool test4 = ratio < 10.0f; // Reasonable upper bound
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Original: " + std::to_string(originalMemory) + 
                                 " bytes, Compressed: " + std::to_string(compressedMemory) + 
                                 " bytes, Ratio: " + std::to_string(ratio) + "x";
            addTestResult("3.1.5 Memory Usage Validation", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.5 Memory Usage Validation", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 6: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            const int iterations = 10;
            std::vector<double> compressionTimes;
            std::vector<double> samplingTimes;
            
            for (int i = 0; i < iterations; ++i) {
                auto texture = createTestTexture(128, 128, false);
                
                // Benchmark compression
                auto compStart = std::chrono::high_resolution_clock::now();
                texture->compress();
                auto compEnd = std::chrono::high_resolution_clock::now();
                
                double compTime = std::chrono::duration<double, std::nano>(compEnd - compStart).count();
                compressionTimes.push_back(compTime);
                
                // Benchmark sampling
                auto sampStart = std::chrono::high_resolution_clock::now();
                for (int j = 0; j < 1000; ++j) {
                    Vec2 uv(j * 0.001f, j * 0.001f);
                    texture->sample(uv);
                }
                auto sampEnd = std::chrono::high_resolution_clock::now();
                
                double sampTime = std::chrono::duration<double, std::nano>(sampEnd - sampStart).count() / 1000.0;
                samplingTimes.push_back(sampTime);
            }
            
            // Calculate averages
            double avgCompTime = 0.0;
            double avgSampTime = 0.0;
            for (int i = 0; i < iterations; ++i) {
                avgCompTime += compressionTimes[i];
                avgSampTime += samplingTimes[i];
            }
            avgCompTime /= iterations;
            avgSampTime /= iterations;
            
            // Performance targets
            bool test1 = avgCompTime < 50000000.0; // < 50ms compression
            bool test2 = avgSampTime < 1000.0;     // < 1μs sampling
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Compression: " + std::to_string(avgCompTime / 1000000.0) + 
                                 "ms, Sampling: " + std::to_string(avgSampTime) + "ns";
            addTestResult("3.1.6 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.1.6 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Texture Compression Test Suite ===" << std::endl;
        std::cout << "Testing texture compression system..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testBasicCompression();
        allPassed &= testDXT1Compression();
        allPassed &= testDXT5Compression();
        allPassed &= testCompressionQuality();
        allPassed &= testMemoryUsage();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns / 1000.0 << "μs)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Texture compression system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    TextureCompressionTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
