// src/test_texture_lod.cpp
// PhotonRender - Texture LOD System Test
// Tests LOD generation, mipmap filtering, and performance

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include "core/texture/texture_lod.hpp"
#include "core/math/vec2.hpp"
#include "core/math/color3.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for texture LOD system
 */
class TextureLODTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }
    
    /**
     * @brief Create test texture with gradient pattern
     */
    std::shared_ptr<LODTexture> createTestTexture(int width, int height, bool withAlpha = false) {
        int channels = withAlpha ? 4 : 3;
        std::vector<float> data(width * height * channels);
        
        // Create a gradient pattern
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int index = (y * width + x) * channels;
                
                // Create a checkerboard pattern for better LOD testing
                bool checker = ((x / 8) + (y / 8)) % 2 == 0;
                float intensity = checker ? 1.0f : 0.0f;
                
                data[index] = intensity;         // Red
                data[index + 1] = intensity;     // Green
                data[index + 2] = intensity;     // Blue
                
                if (withAlpha) {
                    data[index + 3] = 0.8f; // Alpha
                }
            }
        }
        
        return std::make_shared<LODTexture>(width, height, channels, data.data());
    }

public:
    /**
     * @brief Test 1: Basic LOD texture creation
     */
    bool testLODTextureCreation() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(64, 64, false);
            
            bool test1 = (texture != nullptr);
            bool test2 = (texture->getMipmapLevels() == 1); // No mipmaps generated yet
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.3.1 LOD Texture Creation", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.1 LOD Texture Creation", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Mipmap generation
     */
    bool testMipmapGeneration() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(128, 128, false);
            
            LODSettings settings = LODSettings::defaultSettings();
            bool generated = texture->generateMipmaps(settings);
            
            bool test1 = generated;
            bool test2 = (texture->getMipmapLevels() > 1);
            
            // Check that each level is half the size of the previous
            bool test3 = true;
            for (int i = 1; i < texture->getMipmapLevels(); ++i) {
                const LODLevel* level = texture->getLODLevel(i);
                const LODLevel* prevLevel = texture->getLODLevel(i - 1);
                
                if (level && prevLevel) {
                    bool widthOk = (level->width == std::max(1, prevLevel->width / 2));
                    bool heightOk = (level->height == std::max(1, prevLevel->height / 2));
                    if (!widthOk || !heightOk) {
                        test3 = false;
                        break;
                    }
                }
            }
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Levels: " + std::to_string(texture->getMipmapLevels());
            addTestResult("3.3.2 Mipmap Generation", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.2 Mipmap Generation", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: LOD sampling
     */
    bool testLODSampling() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(64, 64, false);
            texture->generateMipmaps();
            
            Vec2 uv(0.5f, 0.5f);
            
            // Test sampling at different LOD levels
            Color3 sample0 = texture->sampleAtLOD(uv, 0.0f);
            Color3 sample1 = texture->sampleAtLOD(uv, 1.0f);
            Color3 sample2 = texture->sampleAtLOD(uv, 2.0f);
            
            // Test distance-based LOD sampling
            Color3 sampleNear = texture->sampleLOD(uv, 1.0f);
            Color3 sampleFar = texture->sampleLOD(uv, 100.0f);
            
            // All samples should be valid colors
            bool test1 = std::isfinite(sample0.r) && std::isfinite(sample0.g) && std::isfinite(sample0.b);
            bool test2 = std::isfinite(sample1.r) && std::isfinite(sample1.g) && std::isfinite(sample1.b);
            bool test3 = std::isfinite(sample2.r) && std::isfinite(sample2.g) && std::isfinite(sample2.b);
            bool test4 = std::isfinite(sampleNear.r) && std::isfinite(sampleNear.g) && std::isfinite(sampleNear.b);
            bool test5 = std::isfinite(sampleFar.r) && std::isfinite(sampleFar.g) && std::isfinite(sampleFar.b);
            
            bool passed = test1 && test2 && test3 && test4 && test5;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.3.3 LOD Sampling", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.3 LOD Sampling", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Different filtering methods
     */
    bool testFilteringMethods() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture1 = createTestTexture(32, 32, false);
            auto texture2 = createTestTexture(32, 32, false);
            auto texture3 = createTestTexture(32, 32, false);
            
            // Test different filtering methods
            LODSettings boxSettings;
            boxSettings.filter = MipmapFilter::BOX;
            
            LODSettings bilinearSettings;
            bilinearSettings.filter = MipmapFilter::BILINEAR;
            
            LODSettings bicubicSettings;
            bicubicSettings.filter = MipmapFilter::BICUBIC;
            
            bool test1 = texture1->generateMipmaps(boxSettings);
            bool test2 = texture2->generateMipmaps(bilinearSettings);
            bool test3 = texture3->generateMipmaps(bicubicSettings);
            
            // Test that all methods produce valid results
            Vec2 uv(0.5f, 0.5f);
            Color3 sample1 = texture1->sampleAtLOD(uv, 1.0f);
            Color3 sample2 = texture2->sampleAtLOD(uv, 1.0f);
            Color3 sample3 = texture3->sampleAtLOD(uv, 1.0f);
            
            bool test4 = std::isfinite(sample1.r) && std::isfinite(sample1.g) && std::isfinite(sample1.b);
            bool test5 = std::isfinite(sample2.r) && std::isfinite(sample2.g) && std::isfinite(sample2.b);
            bool test6 = std::isfinite(sample3.r) && std::isfinite(sample3.g) && std::isfinite(sample3.b);
            
            bool passed = test1 && test2 && test3 && test4 && test5 && test6;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.3.4 Filtering Methods", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.4 Filtering Methods", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: LOD bias and settings
     */
    bool testLODBiasAndSettings() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(64, 64, false);
            texture->generateMipmaps();
            
            // Test LOD bias
            texture->setLODBias(1.0f);
            bool test1 = (texture->getLODBias() == 1.0f);
            
            texture->setLODBias(-0.5f);
            bool test2 = (texture->getLODBias() == -0.5f);
            
            // Test clamping
            texture->setLODBias(5.0f);
            bool test3 = (texture->getLODBias() == 2.0f); // Should clamp to 2.0
            
            texture->setLODBias(-5.0f);
            bool test4 = (texture->getLODBias() == -2.0f); // Should clamp to -2.0
            
            // Test trilinear filtering
            texture->setTrilinearFiltering(true);
            bool test5 = texture->isTrilinearFilteringEnabled();
            
            texture->setTrilinearFiltering(false);
            bool test6 = !texture->isTrilinearFilteringEnabled();
            
            // Test LOD selection methods
            texture->setLODSelectionMethod(LODSelectionMethod::DISTANCE);
            bool test7 = (texture->getLODSelectionMethod() == LODSelectionMethod::DISTANCE);
            
            texture->setLODSelectionMethod(LODSelectionMethod::SCREEN_SIZE);
            bool test8 = (texture->getLODSelectionMethod() == LODSelectionMethod::SCREEN_SIZE);
            
            bool passed = test1 && test2 && test3 && test4 && test5 && test6 && test7 && test8;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.3.5 LOD Bias and Settings", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.5 LOD Bias and Settings", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 6: Memory usage tracking
     */
    bool testMemoryUsage() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(128, 128, false);
            
            // Get memory usage before mipmaps
            size_t memoryBefore = texture->getTotalMemoryUsage();
            
            // Generate mipmaps
            texture->generateMipmaps();
            
            // Get memory usage after mipmaps
            size_t memoryAfter = texture->getTotalMemoryUsage();
            
            // Memory should increase after generating mipmaps
            bool test1 = (memoryAfter > memoryBefore);
            
            // Check individual level memory usage
            bool test2 = true;
            for (int i = 0; i < texture->getMipmapLevels(); ++i) {
                const LODLevel* level = texture->getLODLevel(i);
                if (level && level->memoryUsage == 0) {
                    test2 = false;
                    break;
                }
            }
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Before: " + std::to_string(memoryBefore) + 
                                 " bytes, After: " + std::to_string(memoryAfter) + " bytes";
            addTestResult("3.3.6 Memory Usage Tracking", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.6 Memory Usage Tracking", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 7: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto texture = createTestTexture(256, 256, false);
            
            // Benchmark mipmap generation
            auto genStart = std::chrono::high_resolution_clock::now();
            texture->generateMipmaps();
            auto genEnd = std::chrono::high_resolution_clock::now();
            
            double genTime = std::chrono::duration<double, std::nano>(genEnd - genStart).count();
            
            // Benchmark LOD sampling
            const int iterations = 10000;
            Vec2 uv(0.5f, 0.5f);
            
            auto sampStart = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < iterations; ++i) {
                float distance = 1.0f + i * 0.01f;
                texture->sampleLOD(uv, distance);
            }
            auto sampEnd = std::chrono::high_resolution_clock::now();
            
            double sampTime = std::chrono::duration<double, std::nano>(sampEnd - sampStart).count() / iterations;
            
            // Performance targets
            bool test1 = genTime < 100000000.0; // < 100ms for generation
            bool test2 = sampTime < 1000.0;     // < 1μs per sample
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Generation: " + std::to_string(genTime / 1000000.0) + 
                                 "ms, Sampling: " + std::to_string(sampTime) + "ns";
            addTestResult("3.3.7 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.3.7 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Texture LOD Test Suite ===" << std::endl;
        std::cout << "Testing LOD generation and management..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testLODTextureCreation();
        allPassed &= testMipmapGeneration();
        allPassed &= testLODSampling();
        allPassed &= testFilteringMethods();
        allPassed &= testLODBiasAndSettings();
        allPassed &= testMemoryUsage();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns / 1000.0 << "μs)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Texture LOD system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    TextureLODTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
