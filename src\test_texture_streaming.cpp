// src/test_texture_streaming.cpp
// PhotonRender - Texture Streaming System Test
// Tests texture streaming functionality, caching, and performance

#include <iostream>
#include <memory>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include <thread>
#include "core/texture/texture_streaming.hpp"
#include "core/math/vec3.hpp"

using namespace photon;

/**
 * @brief Test results structure
 */
struct TestResult {
    std::string name;
    bool passed;
    double duration_ns;
    std::string details;
    
    TestResult(const std::string& n, bool p, double d, const std::string& det = "")
        : name(n), passed(p), duration_ns(d), details(det) {}
};

/**
 * @brief Test suite for texture streaming system
 */
class TextureStreamingTestSuite {
private:
    std::vector<TestResult> results;
    
    void addTestResult(const std::string& name, bool passed, double duration_ns = 0.0, const std::string& details = "") {
        results.push_back(TestResult(name, passed, duration_ns, details));
    }

public:
    /**
     * @brief Test 1: Basic streaming manager initialization
     */
    bool testInitialization() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            StreamingParams params = StreamingParams::defaultParams();
            params.maxLoadingThreads = 2; // Reduce for testing
            
            TextureStreamingManager manager(params);
            
            bool test1 = manager.initialize();
            
            // Test parameter access
            const auto& retrievedParams = manager.getStreamingParams();
            bool test2 = (retrievedParams.maxMemoryMB == params.maxMemoryMB);
            bool test3 = (retrievedParams.maxLoadingThreads == params.maxLoadingThreads);
            
            manager.shutdown();
            
            bool passed = test1 && test2 && test3;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.1 Initialization", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.1 Initialization", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 2: Memory statistics
     */
    bool testMemoryStatistics() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            StreamingParams params = StreamingParams::defaultParams();
            params.maxMemoryMB = 100; // Small limit for testing
            
            TextureStreamingManager manager(params);
            manager.initialize();
            
            // Get initial stats
            auto stats = manager.getMemoryStats();
            
            bool test1 = (stats.totalMemoryMB == 100);
            bool test2 = (stats.usedMemoryMB == 0);
            bool test3 = (stats.cachedTextures == 0);
            bool test4 = (stats.cacheHitRate == 0.0f);
            
            manager.shutdown();
            
            bool passed = test1 && test2 && test3 && test4;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.2 Memory Statistics", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.2 Memory Statistics", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 3: Texture request system
     */
    bool testTextureRequests() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            TextureStreamingManager manager;
            manager.initialize();
            
            // Test texture request creation
            TextureRequest request1("test_texture1.png", TexturePriority::HIGH, Vec3(10.0f, 0.0f, 0.0f));
            TextureRequest request2("test_texture2.png", TexturePriority::MEDIUM, Vec3(20.0f, 0.0f, 0.0f));
            TextureRequest request3("test_texture3.png", TexturePriority::LOW, Vec3(30.0f, 0.0f, 0.0f));
            
            // Test request properties
            bool test1 = (request1.priority == TexturePriority::HIGH);
            bool test2 = (request2.worldPosition.x == 20.0f);
            bool test3 = (request3.path == "test_texture3.png");
            
            // Test requesting non-existent textures (should return nullptr)
            auto texture1 = manager.requestTexture(request1);
            auto texture2 = manager.requestTexture(request2);
            auto texture3 = manager.requestTexture(request3);
            
            // Should return nullptr for non-existent files
            bool test4 = (texture1 == nullptr);
            bool test5 = (texture2 == nullptr);
            bool test6 = (texture3 == nullptr);
            
            manager.shutdown();
            
            bool passed = test1 && test2 && test3 && test4 && test5 && test6;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.3 Texture Requests", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.3 Texture Requests", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 4: Cache management
     */
    bool testCacheManagement() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            StreamingParams params = StreamingParams::defaultParams();
            params.maxCacheSize = 10; // Small cache for testing
            
            TextureStreamingManager manager(params);
            manager.initialize();
            
            // Test cache operations
            manager.clearCache();
            
            auto stats1 = manager.getMemoryStats();
            bool test1 = (stats1.cachedTextures == 0);
            
            // Test unload operations
            manager.unloadTexture("non_existent.png"); // Should not crash
            
            auto stats2 = manager.getMemoryStats();
            bool test2 = (stats2.cachedTextures == 0);
            
            manager.shutdown();
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.4 Cache Management", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.4 Cache Management", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 5: Update and distance-based operations
     */
    bool testUpdateOperations() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            StreamingParams params = StreamingParams::defaultParams();
            params.unloadDistance = 50.0f;
            params.preloadDistance = 25.0f;
            
            TextureStreamingManager manager(params);
            manager.initialize();
            
            Vec3 cameraPos(0.0f, 0.0f, 0.0f);
            float deltaTime = 0.016f; // 60 FPS
            
            // Test update function
            manager.update(cameraPos, deltaTime);
            
            // Test preload area
            manager.preloadArea(Vec3(10.0f, 0.0f, 0.0f), 15.0f);
            
            // Test unload distant textures
            manager.unloadDistantTextures(cameraPos);
            
            // Should not crash
            bool test1 = true;
            
            manager.shutdown();
            
            bool passed = test1;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.5 Update Operations", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.5 Update Operations", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 6: Threading and concurrency
     */
    bool testThreadingSafety() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            StreamingParams params = StreamingParams::defaultParams();
            params.maxLoadingThreads = 4;
            
            TextureStreamingManager manager(params);
            manager.initialize();
            
            // Test concurrent operations
            std::vector<std::thread> threads;
            std::atomic<bool> allPassed{true};
            
            // Create multiple threads making requests
            for (int i = 0; i < 8; ++i) {
                threads.emplace_back([&manager, &allPassed, i]() {
                    try {
                        for (int j = 0; j < 10; ++j) {
                            std::string path = "test_" + std::to_string(i) + "_" + std::to_string(j) + ".png";
                            TextureRequest request(path, TexturePriority::MEDIUM, Vec3(i * 10.0f, j * 5.0f, 0.0f));
                            
                            manager.requestTexture(request);
                            manager.getTexture(path);
                            
                            // Small delay
                            std::this_thread::sleep_for(std::chrono::milliseconds(1));
                        }
                    } catch (...) {
                        allPassed = false;
                    }
                });
            }
            
            // Wait for all threads
            for (auto& thread : threads) {
                thread.join();
            }
            
            // Test statistics access during concurrent operations
            auto stats = manager.getMemoryStats();
            bool test1 = allPassed.load();
            bool test2 = (stats.totalMemoryMB > 0);
            
            manager.shutdown();
            
            bool passed = test1 && test2;
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            addTestResult("3.2.6 Threading Safety", passed, duration);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.6 Threading Safety", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Test 7: Performance benchmark
     */
    bool testPerformance() {
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            TextureStreamingManager manager;
            manager.initialize();
            
            const int iterations = 1000;
            
            // Benchmark request operations
            auto perfStart = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i) {
                std::string path = "perf_test_" + std::to_string(i) + ".png";
                TextureRequest request(path, TexturePriority::MEDIUM, Vec3(i * 0.1f, 0.0f, 0.0f));
                
                manager.requestTexture(request);
                manager.getTexture(path);
            }
            
            auto perfEnd = std::chrono::high_resolution_clock::now();
            double totalTime = std::chrono::duration<double, std::nano>(perfEnd - perfStart).count();
            double avgTime = totalTime / (iterations * 2); // 2 operations per iteration
            
            // Performance target: < 10μs per operation
            bool passed = avgTime < 10000.0;
            
            manager.shutdown();
            
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            
            std::string details = "Avg: " + std::to_string(avgTime) + "ns per operation";
            addTestResult("3.2.7 Performance Benchmark", passed, duration, details);
            return passed;
            
        } catch (const std::exception& e) {
            auto end = std::chrono::high_resolution_clock::now();
            double duration = std::chrono::duration<double, std::nano>(end - start).count();
            addTestResult("3.2.7 Performance Benchmark", false, duration, e.what());
            return false;
        }
    }
    
    /**
     * @brief Run all tests
     */
    bool runAllTests() {
        std::cout << "=== PhotonRender Texture Streaming Test Suite ===" << std::endl;
        std::cout << "Testing texture streaming system..." << std::endl << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testInitialization();
        allPassed &= testMemoryStatistics();
        allPassed &= testTextureRequests();
        allPassed &= testCacheManagement();
        allPassed &= testUpdateOperations();
        allPassed &= testThreadingSafety();
        allPassed &= testPerformance();
        
        return allPassed;
    }
    
    /**
     * @brief Print test results
     */
    void printResults() {
        std::cout << std::endl << "=== TEST RESULTS ===" << std::endl;
        
        int passed = 0;
        int total = results.size();
        
        for (const auto& result : results) {
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.name;
            
            if (result.duration_ns > 0) {
                std::cout << " (" << std::fixed << std::setprecision(2) 
                          << result.duration_ns / 1000.0 << "μs)";
            }
            
            if (!result.details.empty()) {
                std::cout << " - " << result.details;
            }
            
            std::cout << std::endl;
            
            if (result.passed) passed++;
        }
        
        std::cout << std::endl << "Summary: " << passed << "/" << total 
                  << " tests passed (" << (100.0 * passed / total) << "%)" << std::endl;
        
        if (passed == total) {
            std::cout << "🎉 ALL TESTS PASSED! Texture streaming system is working correctly." << std::endl;
        } else {
            std::cout << "❌ Some tests failed. Please check the implementation." << std::endl;
        }
    }
};

/**
 * @brief Main test function
 */
int main() {
    TextureStreamingTestSuite testSuite;
    
    bool success = testSuite.runAllTests();
    testSuite.printResults();
    
    return success ? 0 : 1;
}
